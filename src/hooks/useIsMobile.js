import { useState, useEffect } from 'react';

const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      // فحص عرض الشاشة
      const screenWidth = window.innerWidth;
      
      // فحص User Agent للتأكد من الجهاز
      const userAgent = navigator.userAgent.toLowerCase();
      const mobileKeywords = [
        'android', 'webos', 'iphone', 'ipad', 'ipod', 
        'blackberry', 'windows phone', 'mobile'
      ];
      
      const isMobileDevice = mobileKeywords.some(keyword => 
        userAgent.includes(keyword)
      );
      
      // الجهاز محمول إذا كان عرض الشاشة أقل من 1024px أو كان الجهاز محمول
      const isMobileScreen = screenWidth < 1024;
      
      setIsMobile(isMobileScreen || isMobileDevice);
    };

    // فحص أولي
    checkIsMobile();

    // فحص عند تغيير حجم الشاشة
    window.addEventListener('resize', checkIsMobile);

    // تنظيف المستمع
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  return isMobile;
};

export default useIsMobile;
