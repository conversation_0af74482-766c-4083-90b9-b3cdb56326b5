@import url('https://fonts.googleapis.com/css2?family=Pacifico&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css');



@tailwind base;
@tailwind components;
@tailwind utilities;

/* ثم بعدهم يمكن تضع أي CSS مخصص */

/* Spinner Animation */
.spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}






/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Tajawal', sans-serif;
  scroll-behavior: smooth;
  direction: rtl;
  margin: 0;
  min-height: 100vh;
  background-color: #f9fafb;
}

/* Hero Section Background */
.hero-section {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7)),
    url('https://readdy.ai/api/search-image?query=luxurious%20modern%20kitchen%20with%20elegant%20design%2C%20marble%20countertops%2C%20wooden%20cabinets%2C%20high-end%20appliances%2C%20soft%20lighting%2C%20spacious%20layout%2C%20minimalist%20style%2C%20professional%20photography%2C%20high%20resolution%2C%20advertisement%20quality&width=1920&height=1080&seq=1&orientation=landscape');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

/* Enhanced Navigation Styles */
.glass-nav {
  backdrop-filter: blur(20px);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Smooth animations for mobile menu items */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-item {
  animation: slideInUp 0.3s ease-out forwards;
}

/* Gradient text animation */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gradient-text-animated {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #ec4899, #06b6d4);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Enhanced animations for Why Choose Us section */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.95) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out forwards;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse animation for icons */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced Swiper Styles for Kitchen Gallery */
.kitchen-swiper {
  padding: 20px 0 60px 0;
}

.kitchen-swiper .swiper-slide {
  transition: all 0.3s ease;
}

.kitchen-swiper .swiper-slide-active {
  transform: scale(1.05);
}

.kitchen-swiper .swiper-pagination {
  bottom: 20px;
}

.kitchen-swiper .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, #f97316, #dc2626);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.kitchen-swiper .swiper-pagination-bullet-active {
  opacity: 1;
  transform: scale(1.2);
}

.thumbs-swiper {
  padding: 10px 0;
}

.thumbs-swiper .swiper-slide {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.thumbs-swiper .swiper-slide-thumb-active {
  opacity: 1;
  transform: scale(1.1);
}

/* Custom scrollbar for better UX */
.kitchen-swiper::-webkit-scrollbar,
.thumbs-swiper::-webkit-scrollbar {
  height: 4px;
}

.kitchen-swiper::-webkit-scrollbar-track,
.thumbs-swiper::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.kitchen-swiper::-webkit-scrollbar-thumb,
.thumbs-swiper::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #f97316, #dc2626);
  border-radius: 2px;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .kitchen-swiper {
    padding: 15px 0 50px 0;
  }

  .kitchen-swiper .swiper-slide-active {
    transform: scale(1.02);
  }
}

/* Kitchen Gallery Styles */
.masonry-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  grid-auto-rows: 280px;
  grid-auto-flow: dense;
  gap: 20px;
  perspective: 1000px;
  padding: 20px 0;
}

.masonry-grid .item:nth-child(3n+1) {
  grid-row: span 2;
}

.masonry-grid .item:nth-child(4n+1) {
  grid-column: span 2;
}

/* Kitchen Card Styles */
.kitchen-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  transform: translateY(0) scale(1);
  background: #ffffff;
}

.kitchen-card:hover {
  transform: translateY(-12px) scale(1.03);
  box-shadow: 0 20px 40px rgba(59,130,246,0.25);
}

.kitchen-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59,130,246,0.1), rgba(245,158,11,0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.kitchen-card:hover::before {
  opacity: 1;
}

.kitchen-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg) translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 1;
}

.kitchen-card:hover::after {
  transform: rotate(45deg) translateX(100%);
}

.horizontal-scroll {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 20px 0;
}

.horizontal-scroll::-webkit-scrollbar {
  display: none;
}

.cabinet-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0,0,0,0.12);
  transition: all 0.3s ease-out;
  cursor: pointer;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
}

.cabinet-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 35px rgba(59,130,246,0.2);
}

.cabinet-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59,130,246,0.08), rgba(245,158,11,0.08));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.cabinet-card:hover::after {
  opacity: 1;
}

.cabinet-scroll-container {
  position: relative;
  overflow: hidden;
}

.cabinet-scroll-container::before,
.cabinet-scroll-container::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50px;
  z-index: 10;
  pointer-events: none;
}

.cabinet-scroll-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
}

.cabinet-scroll-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0));
}

.image-hover {
  transition: all 0.4s ease-out;
  position: relative;
  overflow: hidden;
}

.image-hover:hover {
  transform: scale(1.08);
  filter: brightness(1.1) contrast(1.05) saturate(1.1);
}

.image-card {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.4), transparent);
  padding: 20px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
  z-index: 2;
}

.image-card:hover .image-overlay {
  background: linear-gradient(to top, rgba(0,0,0,0.9), rgba(0,0,0,0.5), transparent);
  padding: 25px;
}

/* Image Overlay Text Styles */
.image-overlay h3 {
  color: white !important;
  font-size: 1.25rem !important;
  font-weight: bold !important;
  margin-bottom: 8px !important;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.image-overlay p {
  color: rgba(255,255,255,0.9) !important;
  font-size: 0.9rem !important;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.image-card:hover .image-overlay h3 {
  transform: translateY(-5px);
  color: #60a5fa !important;
}

.image-card:hover .image-overlay p {
  transform: translateY(-3px);
  color: white !important;
}

/* Utility Classes */
.primary {
  color: #3b82f6 !important;
}

.bg-primary {
  background-color: #3b82f6 !important;
}

.text-primary {
  color: #3b82f6 !important;
}

.hover\:bg-primary:hover {
  background-color: #3b82f6 !important;
}

.hover\:text-primary:hover {
  color: #3b82f6 !important;
}

.rounded-button {
  border-radius: 8px !important;
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.stagger-animation {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stagger-animation.animate {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.kitchen-hover-effect {
  transition: all 0.3s ease;
}

.kitchen-hover-effect:hover {
  animation: bounce 0.6s ease;
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-10px) scale(1.02);
  }
  80% {
    transform: translateY(-5px) scale(1.01);
  }
}

.cabinet-slide-in {
  transform: translateX(50px);
  opacity: 0;
  transition: all 0.4s ease-out;
}

.cabinet-slide-in.visible {
  transform: translateX(0);
  opacity: 1;
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.shimmer-effect:hover::before {
  left: 100%;
}

/* Hero Section Animations */
.animate-fade-in {
  animation: fadeInUp 1s ease-out;
}

.animate-fade-in-delay {
  animation: fadeInUp 1s ease-out 0.3s both;
}

.animate-fade-in-delay-2 {
  animation: fadeInUp 1s ease-out 0.6s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
  .hero-section {
    background-attachment: scroll;
  }

  .masonry-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }

  .cabinet-scroll-container {
    padding: 0 10px;
  }
}

.stagger-animation {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.stagger-animation.animate {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.kitchen-hover-effect {
  transition: all 0.3s ease;
}

.kitchen-hover-effect:hover {
  animation: bounce 0.6s ease;
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0) scale(1); }
  40% { transform: translateY(-10px) scale(1.02); }
  80% { transform: translateY(-5px) scale(1.01); }
}

.cabinet-slide-in {
  transform: translateX(50px);
  opacity: 0;
  transition: all 0.4s ease-out;
}

.cabinet-slide-in.visible {
  transform: translateX(0);
  opacity: 1;
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
  z-index: 1;
}

.shimmer-effect:hover::before {
  left: 100%;
}

.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.rounded-button {
  border-radius: 8px;
}

.primary {
  color: var(--primary);
}

.bg-primary {
  background-color: var(--primary);
}

.text-primary {
  color: var(--primary);
}

.hover\:bg-primary:hover {
  background-color: var(--primary);
}

.hover\:text-primary:hover {
  color: var(--primary);
}

/* Admin Dashboard Styles */
.admin-dashboard {
  font-family: 'Tajawal', sans-serif;
  direction: rtl;
}

/* Admin Form Styles */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #ffffff;
  color: #374151;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: #ffffff;
}

.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #ffffff;
  color: #374151;
  resize: vertical;
  min-height: 100px;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background-color: #ffffff;
}

/* Admin Button Styles */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  border: 2px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

/* Admin Card Styles */
.admin-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.admin-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* Image Upload Area */
.image-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
}

.image-upload-area:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* Spinner Animation */
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Admin Sidebar Styles */
.admin-sidebar {
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  color: white;
}

.admin-sidebar-item {
  padding: 12px 20px;
  margin: 4px 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.admin-sidebar-item:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(-4px);
}

.admin-sidebar-item.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Admin Header Styles */
.admin-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Admin Stats Cards */
.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* Admin Table Styles */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: right;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.admin-table td {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
}

.admin-table tr:hover {
  background: #f9fafb;
}

/* Responsive Admin Styles */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transition: transform 0.3s ease;
  }

  .admin-sidebar.open {
    transform: translateX(0);
  }

  .admin-card {
    margin: 0 16px;
  }

  .stats-card {
    padding: 16px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* إغلاق Modal عند النقر خارجه */
.modal-overlay {
  cursor: pointer;
}

.modal-content {
  cursor: default;
}

/* Mobile App Styles */
.mobile-app-container {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Mobile Swiper Styles */
.kitchens-mobile-swiper .swiper-pagination {
  bottom: -30px !important;
}

.cabinets-mobile-swiper .swiper-pagination {
  bottom: -30px !important;
}

.kitchens-mobile-swiper .swiper-pagination-bullet,
.cabinets-mobile-swiper .swiper-pagination-bullet {
  width: 8px !important;
  height: 8px !important;
  margin: 0 4px !important;
}

.kitchens-mobile-swiper .swiper-pagination-bullet-active {
  background: linear-gradient(45deg, #f97316, #dc2626) !important;
}

.cabinets-mobile-swiper .swiper-pagination-bullet-active {
  background: linear-gradient(45deg, #8b5cf6, #3b82f6) !important;
}

/* Cabinet Swiper Styles */
.cabinet-swiper {
  padding: 20px 0 60px 0;
}

.cabinet-swiper .swiper-slide {
  transition: all 0.3s ease;
}

.cabinet-swiper .swiper-slide-active {
  transform: scale(1.05);
}

.cabinet-swiper .swiper-pagination {
  bottom: 20px;
}

.cabinet-swiper .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: linear-gradient(45deg, #8b5cf6, #3b82f6);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.cabinet-swiper .swiper-pagination-bullet-active {
  opacity: 1;
  transform: scale(1.2);
}

/* Mobile safe area */
@supports (padding: max(0px)) {
  .mobile-safe-area-top {
    padding-top: max(env(safe-area-inset-top), 0px);
  }

  .mobile-safe-area-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 0px);
  }
}
