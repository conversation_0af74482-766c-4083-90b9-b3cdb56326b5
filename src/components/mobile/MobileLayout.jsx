import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

const MobileLayout = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  // تحديث الوقت كل دقيقة
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  // تحديد العنوان حسب الصفحة الحالية
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
        return 'عجائب الخبراء';
      case '/kitchens':
        return 'المطابخ';
      case '/cabinets':
        return 'الخزائن';
      default:
        return 'عجائب الخبراء';
    }
  };

  // تحديد الأيقونة حسب الصفحة الحالية
  const getPageIcon = () => {
    switch (location.pathname) {
      case '/':
        return 'ri-home-4-fill';
      case '/kitchens':
        return 'ri-restaurant-fill';
      case '/cabinets':
        return 'ri-archive-fill';
      default:
        return 'ri-home-4-fill';
    }
  };

  // عناصر التنقل السفلي
  const navigationItems = [
    {
      id: 'home',
      path: '/',
      icon: 'ri-home-4-line',
      activeIcon: 'ri-home-4-fill',
      label: 'الرئيسية',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'kitchens',
      path: '/kitchens',
      icon: 'ri-restaurant-line',
      activeIcon: 'ri-restaurant-fill',
      label: 'المطابخ',
      color: 'from-orange-500 to-red-500'
    },
    {
      id: 'cabinets',
      path: '/cabinets',
      icon: 'ri-archive-line',
      activeIcon: 'ri-archive-fill',
      label: 'الخزائن',
      color: 'from-purple-500 to-blue-500'
    },
    {
      id: 'contact',
      path: '#contact',
      icon: 'ri-phone-line',
      activeIcon: 'ri-phone-fill',
      label: 'اتصل بنا',
      color: 'from-green-500 to-emerald-500'
    }
  ];

  const handleNavigation = (item) => {
    if (item.path.startsWith('#')) {
      // للروابط الداخلية مثل #contact
      if (location.pathname !== '/') {
        navigate('/');
        setTimeout(() => {
          const element = document.querySelector(item.path);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      } else {
        const element = document.querySelector(item.path);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    } else {
      navigate(item.path);
    }
  };

  return (
    <div className="lg:hidden min-h-screen bg-gray-50 flex flex-col">
      {/* شريط علوي - Status Bar + Header */}
      <div className="bg-white shadow-lg relative z-50">
        {/* Status Bar */}
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 text-white px-4 py-1 flex justify-between items-center text-xs">
          <div className="flex items-center space-x-1 rtl:space-x-reverse">
            <div className="flex space-x-1">
              <div className="w-1 h-1 bg-white rounded-full"></div>
              <div className="w-1 h-1 bg-white rounded-full"></div>
              <div className="w-1 h-1 bg-white rounded-full"></div>
              <div className="w-1 h-1 bg-white/60 rounded-full"></div>
            </div>
            <span className="text-xs">4G</span>
          </div>
          <div className="font-medium">
            {currentTime.toLocaleTimeString('ar-SA', { 
              hour: '2-digit', 
              minute: '2-digit',
              hour12: false 
            })}
          </div>
          <div className="flex items-center space-x-1 rtl:space-x-reverse">
            <i className="ri-bluetooth-line text-xs"></i>
            <i className="ri-wifi-line text-xs"></i>
            <div className="flex items-center">
              <div className="w-6 h-3 border border-white/60 rounded-sm relative">
                <div className="w-4 h-1.5 bg-green-400 rounded-sm absolute top-0.5 right-0.5"></div>
              </div>
              <span className="text-xs mr-1">85%</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <i className={`${getPageIcon()} text-xl`}></i>
              </div>
              <div>
                <h1 className="text-lg font-bold">{getPageTitle()}</h1>
                <p className="text-xs text-white/80">مرحباً بك في موقعنا</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <button className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <i className="ri-search-line text-lg"></i>
              </button>
              <button className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <i className="ri-notification-line text-lg"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="flex-1 overflow-y-auto pb-20">
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-full"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* شريط التنقل السفلي */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-2xl z-50">
        <div className="flex items-center justify-around py-2">
          {navigationItems.map((item) => {
            const isActive = location.pathname === item.path || 
              (item.path === '#contact' && location.hash === '#contact');
            
            return (
              <motion.button
                key={item.id}
                onClick={() => handleNavigation(item)}
                className="flex flex-col items-center py-2 px-3 relative"
                whileTap={{ scale: 0.95 }}
              >
                {/* Active Indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className={`absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r ${item.color} rounded-full`}
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}
                
                {/* Icon */}
                <div className={`w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 ${
                  isActive 
                    ? `bg-gradient-to-r ${item.color} text-white shadow-lg` 
                    : 'text-gray-500'
                }`}>
                  <i className={`${isActive ? item.activeIcon : item.icon} text-lg`}></i>
                </div>
                
                {/* Label */}
                <span className={`text-xs mt-1 font-medium transition-colors duration-300 ${
                  isActive ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {item.label}
                </span>
                
                {/* Badge for notifications (example) */}
                {item.id === 'contact' && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">!</span>
                  </div>
                )}
              </motion.button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MobileLayout;
