import { useState, useEffect, useRef } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// استيراد البيانات
import { getKitchensData, getFooterData } from '../../../database/api-client.js';
import { getImageURL } from '../../config/api.js';

const MobileKitchensPage = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const [kitchens, setKitchens] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [socialMedia, setSocialMedia] = useState([]);
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل البيانات
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل المطابخ
        const kitchensData = await getKitchensData();
        if (kitchensData && kitchensData.length > 0) {
          const formattedKitchens = kitchensData.map(kitchen => ({
            id: kitchen.id,
            title: kitchen.title,
            description: kitchen.description,
            images: kitchen.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          setKitchens(formattedKitchens);
        }

        // تحميل بيانات التواصل
        const footerData = await getFooterData();
        if (footerData) {
          if (footerData.contactInfo) {
            const phoneContact = footerData.contactInfo.find(contact => contact.icon === 'ri-phone-line');
            if (phoneContact) {
              setWhatsappNumber(phoneContact.text);
            }
          }
          if (footerData.socialMedia && footerData.socialMedia.length > 0) {
            setSocialMedia(footerData.socialMedia);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setError('حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // دالة فتح الواتساب
  const openWhatsApp = (kitchen) => {
    const currentUrl = window.location.href;
    const productUrl = `${currentUrl}#kitchen-${kitchen.id}`;
    const message = `مرحباً، أنا مهتم بهذا المطبخ: ${kitchen.title}\n\nرابط المنتج: ${productUrl}`;

    let phoneNumber = whatsappNumber.replace(/[^0-9]/g, '');
    if (phoneNumber.startsWith('0966')) {
      phoneNumber = phoneNumber.substring(1);
    } else if (phoneNumber.startsWith('966')) {
      phoneNumber = phoneNumber;
    } else if (phoneNumber.startsWith('0')) {
      phoneNumber = '966' + phoneNumber.substring(1);
    } else if (phoneNumber.length === 9) {
      phoneNumber = '966' + phoneNumber;
    }

    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  // شاشة التحميل
  if (loading) {
    return (
      <div className="bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <i className="ri-restaurant-line text-2xl text-white"></i>
          </div>
          <p className="text-gray-600">جاري تحميل المطابخ...</p>
        </div>
      </div>
    );
  }

  // شاشة الخطأ
  if (error) {
    return (
      <div className="bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center px-4">
          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-error-warning-line text-2xl text-white"></i>
          </div>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 text-white px-6 py-2 rounded-lg"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen" ref={sectionRef}>
      {/* Header Section */}
      <div className="bg-gradient-to-br from-orange-500 via-red-500 to-pink-500 text-white px-4 py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-restaurant-line text-2xl"></i>
          </div>
          <h1 className="text-xl font-bold mb-2">مجموعة المطابخ</h1>
          <p className="text-white/90 text-sm">
            استكشف تشكيلتنا الواسعة من المطابخ العصرية والكلاسيكية
          </p>
        </motion.div>
      </div>

      {/* إحصائيات المطابخ */}
      <div className="px-4 py-4 bg-white">
        <div className="grid grid-cols-2 gap-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.5 }}
            className="text-center"
          >
            <div className="text-2xl font-bold text-orange-600">{kitchens.length}+</div>
            <div className="text-xs text-gray-600">تصميم متاح</div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-center"
          >
            <div className="text-2xl font-bold text-red-600">100%</div>
            <div className="text-xs text-gray-600">جودة مضمونة</div>
          </motion.div>
        </div>
      </div>

      {/* قائمة المطابخ */}
      <div className="px-4 py-4">
        {kitchens.length > 0 ? (
          <div className="space-y-4">
            {kitchens.map((kitchen, index) => (
              <motion.div
                key={kitchen.id}
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white rounded-2xl overflow-hidden shadow-lg"
                onClick={() => setLightboxImage(kitchen)}
              >
                <div className="relative h-48">
                  <img
                    src={kitchen.images[0]}
                    alt={kitchen.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                  <div className="absolute bottom-3 left-3 right-3">
                    <h3 className="text-white font-bold text-lg mb-1">{kitchen.title}</h3>
                    <p className="text-white/90 text-sm line-clamp-2">{kitchen.description}</p>
                  </div>
                  <div className="absolute top-3 right-3">
                    <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <i className="ri-zoom-in-line text-white"></i>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex space-x-2 rtl:space-x-reverse">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setLightboxImage(kitchen);
                      }}
                      className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 rounded-lg text-sm font-medium"
                    >
                      عرض التفاصيل
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        openWhatsApp(kitchen);
                      }}
                      className="bg-green-500 text-white px-4 py-2 rounded-lg"
                    >
                      <i className="ri-whatsapp-line"></i>
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-restaurant-line text-2xl text-gray-400"></i>
            </div>
            <p className="text-gray-500">لا توجد مطابخ متاحة حالياً</p>
          </div>
        )}
      </div>

      {/* Modal للصور */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setLightboxImage(null);
              setModalImageIndex(0);
            }}
          >
            <motion.div
              className="relative bg-white w-full h-full flex flex-col"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 z-20 w-10 h-10 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center"
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
              >
                <i className="ri-close-line text-lg"></i>
              </button>

              {/* Image Slider */}
              <div className="relative flex-1 bg-gradient-to-br from-gray-100 via-white to-gray-50">
                <Swiper
                  modules={[Navigation, Pagination]}
                  spaceBetween={0}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.mobile-kitchen-swiper-button-next',
                    prevEl: '.mobile-kitchen-swiper-button-prev',
                  }}
                  pagination={{
                    clickable: true,
                    dynamicBullets: true,
                  }}
                  onSlideChange={(swiper) => setModalImageIndex(swiper.activeIndex)}
                  initialSlide={modalImageIndex}
                  className="h-full"
                >
                  {lightboxImage.images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <div className="relative h-full flex items-center justify-center p-4">
                        <img
                          src={image}
                          alt={`${lightboxImage.title} - صورة ${index + 1}`}
                          className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                        />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* Navigation Buttons */}
                <div className="mobile-kitchen-swiper-button-prev absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <i className="ri-arrow-right-line text-lg text-white"></i>
                </div>
                <div className="mobile-kitchen-swiper-button-next absolute right-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <i className="ri-arrow-left-line text-lg text-white"></i>
                </div>

                {/* Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Content Section */}
              <div className="p-4 bg-white max-h-[40vh] overflow-y-auto">
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold text-gray-800 mb-2">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {lightboxImage.description}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3 rtl:space-x-reverse">
                  <button
                    onClick={() => openWhatsApp(lightboxImage)}
                    className="flex-1 bg-green-500 text-white py-3 rounded-lg font-medium flex items-center justify-center space-x-2 rtl:space-x-reverse"
                  >
                    <i className="ri-whatsapp-line"></i>
                    <span>طلب عبر واتساب</span>
                  </button>
                  
                  {socialMedia.length > 0 && (
                    <div className="flex space-x-2 rtl:space-x-reverse">
                      {socialMedia.slice(0, 2).map((social, index) => {
                        const getBackgroundClass = (platform) => {
                          const platformLower = platform.toLowerCase();
                          if (platformLower.includes('instagram')) return "bg-gradient-to-r from-purple-500 to-pink-500";
                          if (platformLower.includes('twitter') || platformLower.includes('x')) return "bg-black";
                          if (platformLower.includes('snapchat')) return "bg-yellow-500";
                          if (platformLower.includes('tiktok')) return "bg-black";
                          if (platformLower.includes('facebook')) return "bg-blue-600";
                          return "bg-gray-600";
                        };

                        return (
                          <a
                            key={index}
                            href={social.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`w-12 h-12 rounded-lg flex items-center justify-center text-white ${getBackgroundClass(social.platform)}`}
                          >
                            <i className={`${social.icon} text-lg`}></i>
                          </a>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MobileKitchensPage;
