import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useInView } from 'framer-motion';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';
import { getKitchensData, getCabinetsData, getFooterData } from '../../../database/api-client.js';
import { getImageURL } from '../../config/api.js';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

const MobileHomePage = () => {
  const navigate = useNavigate();
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  const [kitchens, setKitchens] = useState([]);
  const [cabinets, setCabinets] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');

  // تحميل البيانات
  useEffect(() => {
    const loadData = async () => {
      try {
        // تحميل المطابخ
        const kitchensData = await getKitchensData();
        if (kitchensData && kitchensData.length > 0) {
          const formattedKitchens = kitchensData.slice(0, 6).map(kitchen => ({
            id: kitchen.id,
            title: kitchen.title,
            description: kitchen.description,
            images: kitchen.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          setKitchens(formattedKitchens);
        }

        // تحميل الخزائن
        const cabinetsData = await getCabinetsData();
        if (cabinetsData && cabinetsData.length > 0) {
          const formattedCabinets = cabinetsData.slice(0, 6).map(cabinet => ({
            id: cabinet.id,
            title: cabinet.title,
            description: cabinet.description,
            images: cabinet.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          setCabinets(formattedCabinets);
        }

        // تحميل رقم الواتساب
        const footerData = await getFooterData();
        if (footerData?.contactInfo) {
          const phoneContact = footerData.contactInfo.find(contact => contact.icon === 'ri-phone-line');
          if (phoneContact) {
            setWhatsappNumber(phoneContact.text);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, []);

  // دالة فتح الواتساب
  const openWhatsApp = () => {
    let phoneNumber = whatsappNumber.replace(/[^0-9]/g, '');
    if (phoneNumber.startsWith('0966')) {
      phoneNumber = phoneNumber.substring(1);
    } else if (phoneNumber.startsWith('0')) {
      phoneNumber = '966' + phoneNumber.substring(1);
    } else if (phoneNumber.length === 9) {
      phoneNumber = '966' + phoneNumber;
    }
    const message = 'مرحباً، أريد الاستفسار عن خدماتكم';
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="bg-gray-50" ref={sectionRef}>
      {/* Hero Section للهاتف */}
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-home-heart-line text-3xl"></i>
          </div>
          <h1 className="text-2xl font-bold mb-2">مرحباً بك في عجائب الخبراء</h1>
          <p className="text-white/90 text-sm leading-relaxed mb-6">
            نحن متخصصون في تصميم وتنفيذ أجمل المطابخ والخزائن العصرية والكلاسيكية
          </p>
          <motion.button
            onClick={openWhatsApp}
            className="bg-white text-blue-600 px-6 py-3 rounded-full font-semibold flex items-center justify-center mx-auto space-x-2 rtl:space-x-reverse shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <i className="ri-whatsapp-line text-lg"></i>
            <span>تواصل معنا الآن</span>
          </motion.button>
        </motion.div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="px-4 py-6 bg-white">
        <div className="grid grid-cols-3 gap-4">
          {[
            { icon: 'ri-award-line', number: '500+', label: 'مشروع مكتمل', color: 'from-orange-500 to-red-500' },
            { icon: 'ri-customer-service-line', number: '24/7', label: 'دعم فني', color: 'from-green-500 to-emerald-500' },
            { icon: 'ri-star-line', number: '4.9', label: 'تقييم العملاء', color: 'from-yellow-500 to-orange-500' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-full flex items-center justify-center mx-auto mb-2`}>
                <i className={`${stat.icon} text-white text-lg`}></i>
              </div>
              <div className="text-lg font-bold text-gray-800">{stat.number}</div>
              <div className="text-xs text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* قسم المطابخ */}
      <div className="px-4 py-6">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={isInView ? { opacity: 1, x: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-4"
        >
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <i className="ri-restaurant-line text-white"></i>
            </div>
            <h2 className="text-lg font-bold text-gray-800">أحدث المطابخ</h2>
          </div>
          <button
            onClick={() => navigate('/kitchens')}
            className="text-orange-600 text-sm font-medium flex items-center space-x-1 rtl:space-x-reverse"
          >
            <span>عرض الكل</span>
            <i className="ri-arrow-left-line"></i>
          </button>
        </motion.div>

        {kitchens.length > 0 && (
          <Swiper
            modules={[Pagination, Autoplay]}
            spaceBetween={16}
            slidesPerView={1.2}
            pagination={{ clickable: true }}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            className="kitchens-mobile-swiper"
          >
            {kitchens.map((kitchen) => (
              <SwiperSlide key={kitchen.id}>
                <motion.div
                  className="bg-white rounded-2xl overflow-hidden shadow-lg"
                  whileHover={{ y: -5 }}
                >
                  <div className="relative h-40">
                    <img
                      src={kitchen.images[0]}
                      alt={kitchen.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute bottom-2 left-2 right-2">
                      <h3 className="text-white font-semibold text-sm">{kitchen.title}</h3>
                    </div>
                  </div>
                  <div className="p-3">
                    <p className="text-gray-600 text-xs line-clamp-2">{kitchen.description}</p>
                    <button className="w-full mt-3 bg-gradient-to-r from-orange-500 to-red-500 text-white py-2 rounded-lg text-sm font-medium">
                      عرض التفاصيل
                    </button>
                  </div>
                </motion.div>
              </SwiperSlide>
            ))}
          </Swiper>
        )}
      </div>

      {/* قسم الخزائن */}
      <div className="px-4 py-6 bg-white">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={isInView ? { opacity: 1, x: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-between mb-4"
        >
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
              <i className="ri-archive-line text-white"></i>
            </div>
            <h2 className="text-lg font-bold text-gray-800">أحدث الخزائن</h2>
          </div>
          <button
            onClick={() => navigate('/cabinets')}
            className="text-purple-600 text-sm font-medium flex items-center space-x-1 rtl:space-x-reverse"
          >
            <span>عرض الكل</span>
            <i className="ri-arrow-left-line"></i>
          </button>
        </motion.div>

        {cabinets.length > 0 && (
          <Swiper
            modules={[Pagination, Autoplay]}
            spaceBetween={16}
            slidesPerView={1.2}
            pagination={{ clickable: true }}
            autoplay={{ delay: 4000, disableOnInteraction: false }}
            className="cabinets-mobile-swiper"
          >
            {cabinets.map((cabinet) => (
              <SwiperSlide key={cabinet.id}>
                <motion.div
                  className="bg-gray-50 rounded-2xl overflow-hidden shadow-lg"
                  whileHover={{ y: -5 }}
                >
                  <div className="relative h-40">
                    <img
                      src={cabinet.images[0]}
                      alt={cabinet.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className="absolute bottom-2 left-2 right-2">
                      <h3 className="text-white font-semibold text-sm">{cabinet.title}</h3>
                    </div>
                  </div>
                  <div className="p-3">
                    <p className="text-gray-600 text-xs line-clamp-2">{cabinet.description}</p>
                    <button className="w-full mt-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white py-2 rounded-lg text-sm font-medium">
                      عرض التفاصيل
                    </button>
                  </div>
                </motion.div>
              </SwiperSlide>
            ))}
          </Swiper>
        )}
      </div>

      {/* دعوة للعمل */}
      <div className="px-4 py-8 bg-gradient-to-r from-gray-800 to-gray-900 text-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <h3 className="text-xl font-bold mb-2">جاهز لبدء مشروعك؟</h3>
          <p className="text-gray-300 text-sm mb-6">تواصل معنا الآن للحصول على استشارة مجانية</p>
          <div className="flex space-x-3 rtl:space-x-reverse justify-center">
            <motion.button
              onClick={openWhatsApp}
              className="bg-green-500 text-white px-6 py-3 rounded-full font-medium flex items-center space-x-2 rtl:space-x-reverse"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="ri-whatsapp-line"></i>
              <span>واتساب</span>
            </motion.button>
            <motion.button
              onClick={() => {
                const element = document.querySelector('#contact');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-full font-medium flex items-center space-x-2 rtl:space-x-reverse border border-white/30"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="ri-phone-line"></i>
              <span>اتصل بنا</span>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default MobileHomePage;
